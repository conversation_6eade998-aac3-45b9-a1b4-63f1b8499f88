@extends('layouts.app')

@section('title', 'Dashboard')

@section('content')
<div class="container-fluid">
    @if (session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="mb-2">
                <h6 class="text-muted mb-1">Welcome back, {{ Auth::user()?->name ?? 'User' }}</h6>
                <h1 class="display-5 fw-bold mb-0">Dashboard</h1>
            </div>
        </div>
        <div class="col-md-6">
            <div class="row g-3">
                <div class="col-3">
                    <a href="{{ route('invoices.index') }}" class="text-decoration-none">
                        <div class="card bg-primary bg-opacity-10 border-primary border-opacity-25 h-100">
                            <div class="card-body p-3 text-center">
                                <h6 class="text-primary small mb-1">Total Invoices</h6>
                                <h4 class="fw-bold mb-0 text-primary">{{ count($invoices) }}</h4>
                                <small class="text-primary">View All →</small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-3">
                    <div class="card bg-success bg-opacity-10 border-success border-opacity-25 h-100">
                        <div class="card-body p-3 text-center">
                            <h6 class="text-success small mb-1">Total Revenue</h6>
                            <h4 class="fw-bold mb-0 text-success">{{ number_format($totalRevenue, 2) }} CHF</h4>
                            <small class="text-success">Paid Invoices</small>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card bg-warning bg-opacity-10 border-warning border-opacity-25 h-100">
                        <div class="card-body p-3 text-center">
                            <h6 class="text-warning small mb-1">Unpaid</h6>
                            <h4 class="fw-bold mb-0 text-warning">{{ $unpaidInvoices }}</h4>
                            <small class="text-warning">Pending Payment</small>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card bg-info bg-opacity-10 border-info border-opacity-25 h-100">
                        <div class="card-body p-3 text-center">
                            <h6 class="text-info small mb-1">Recurring</h6>
                            <h4 class="fw-bold mb-0 text-info">{{ count($recurringInvoices) }}</h4>
                            <small class="text-info">Active Templates</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sync Status Info -->
    @if(isset($dataSource) && $dataSource === 'bexio_synced' && isset($syncStats))
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-sync-alt me-2"></i>
                    <strong>Data Synced with Bexio:</strong>
                    {{ $syncStats['total_bexio_invoices'] }} invoices found,
                    {{ $syncStats['created'] }} created,
                    {{ $syncStats['updated'] }} updated
                    @if($syncStats['errors'] > 0)
                        , {{ $syncStats['errors'] }} errors
                    @endif
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    @elseif(isset($dataSource) && $dataSource === 'local_database')
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Using Local Data:</strong> Unable to sync with Bexio API. Showing cached data.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    @endif

    <!-- Main Content -->
    <div class="row g-4">
        <!-- Recurring Invoices Section -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-semibold">Upcoming Recurring Invoices</h5>
                    <a href="{{ route('invoices.create-advanced') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Create New
                    </a>
                </div>
                <div class="card-body p-0">
                    @if(count($recurringInvoices) > 0)
                        @foreach($recurringInvoices->take(5) as $index => $invoice)
                            <div class="d-flex align-items-center p-3 {{ $index > 0 ? 'border-top' : '' }}">
                                <div class="me-3">
                                    <div class="bg-dark text-white rounded d-flex align-items-center justify-content-center flex-column" style="width: 45px; height: 45px; font-size: 0.75rem;">
                                        <div class="fw-bold">{{ date('d') }}</div>
                                        <div style="font-size: 0.6rem;">{{ strtoupper(date('M')) }}</div>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $invoice['rebill_title'] }}</h6>
                                    <small class="text-muted">{{ $invoice['rebill_description'] }}</small>
                                    <div class="mt-1">
                                        <span class="badge {{ $invoice['status'] === 'active' ? 'bg-success' : 'bg-secondary' }} bg-opacity-10 text-{{ $invoice['status'] === 'active' ? 'success' : 'secondary' }}">
                                            {{ ucfirst($invoice['status']) }}
                                        </span>
                                        <span class="badge bg-light text-dark ms-1">{{ ucfirst($invoice['period']) }}</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                        @if(count($recurringInvoices) > 5)
                            <div class="text-center p-3 border-top">
                                <a href="{{ route('invoices.index') }}" class="btn btn-outline-secondary btn-sm">View All</a>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-invoice fa-2x text-muted mb-3"></i>
                            <h6 class="text-muted">You don't have any recurring invoices</h6>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Drafts Section -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 fw-semibold">Drafts</h5>
                </div>
                <div class="card-body p-0">
                    @if(count($drafts) > 0)
                        @foreach($drafts->take(5) as $index => $draft)
                            <div class="d-flex align-items-center p-3 {{ $index > 0 ? 'border-top' : '' }}">
                                <div class="me-3">
                                    <i class="fas fa-file-alt fa-lg text-muted"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $draft['draft']['rebill_title'] ?: $draft['draft']['rebill_description'] ?: 'Created ' . date('M j, Y', $draft['created'] / 1000) }}</h6>
                                    <small class="text-muted">#{{ $draft['id'] }}</small>
                                    @if($draft['draft']['customerDetails']['name'])
                                        <small class="text-muted"> - {{ $draft['draft']['customerDetails']['name'] }}</small>
                                    @endif
                                </div>
                                <div class="text-end">
                                    <button class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        @endforeach
                        @if(count($drafts) > 5)
                            <div class="text-center p-3 border-top">
                                <a href="{{ route('invoices.index') }}?status=draft" class="btn btn-outline-secondary btn-sm">View All</a>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-2x text-muted mb-3"></i>
                            <h6 class="text-muted">You don't have any drafts</h6>
                        </div>
                    @endif
                </div>

                <!-- Quick Actions at bottom -->
                <div class="card-footer bg-light border-top">
                    <div class="d-grid gap-2">
                        <a href="{{ route('invoices.create-advanced') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Create New Invoice
                        </a>
                        <div class="row g-2">
                            <div class="col-6">
                                <a href="{{ route('settings.index') }}" class="btn btn-outline-secondary btn-sm w-100">
                                    <i class="fas fa-cog me-1"></i>Settings
                                </a>
                            </div>
                            <div class="col-6">
                                <form method="POST" action="{{ route('logout') }}" class="mb-0">
                                    @csrf
                                    <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
