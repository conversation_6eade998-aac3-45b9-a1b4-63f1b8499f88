<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Services\BexioServiceInterface;
use App\Services\BexioSyncService;
use App\Models\Invoice;

class DashboardController extends Controller
{
    protected BexioServiceInterface $bexioService;
    protected BexioSyncService $syncService;

    public function __construct(BexioServiceInterface $bexioService, BexioSyncService $syncService)
    {
        $this->bexioService = $bexioService;
        $this->syncService = $syncService;
    }

    public function index()
    {
        $user = Auth::user();
        $organization = $user->organization;

        // Check if user has valid Bexio access token
        if (!$user->access_token) {
            Log::warning("User {$user->id} has no Bexio access token for dashboard");
            return $this->fallbackToLocalData($organization);
        }

        // Set access token for Bexio service
        $this->bexioService->setAccessToken($user->access_token);

        try {
            // Sync data with Bexio API (this will update local database)
            $syncStats = $this->syncService->syncInvoicesForOrganization($organization, $user->access_token);
            $this->syncService->syncRecurringInvoicesForOrganization($organization, $user->access_token);

            // Get updated data from local database (now synced with Bexio)
            $invoices = Invoice::forOrganization($organization->id)->get();
            $recurringInvoices = $invoices->where('is_recurring', true);
            $drafts = $invoices->where('status', 'draft');

            // Calculate metrics
            $unpaidInvoices = $invoices->whereNotIn('status', ['paid', 'cancelled'])->count();
            $totalRevenue = $invoices->where('status', 'paid')->sum('total');

            Log::info("Dashboard loaded with synced Bexio data for user {$user->id}", $syncStats);

            return view('dashboard', [
                'invoices' => $invoices,
                'recurringInvoices' => $recurringInvoices,
                'drafts' => $drafts,
                'unpaidInvoices' => $unpaidInvoices,
                'totalRevenue' => $totalRevenue,
                'dataSource' => 'bexio_synced',
                'syncStats' => $syncStats
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to load Bexio data for dashboard: " . $e->getMessage());

            // Fallback to local database data
            return $this->fallbackToLocalData($organization);
        }
    }



    /**
     * Fallback to local database data when Bexio API is unavailable
     */
    private function fallbackToLocalData($organization): \Illuminate\View\View
    {
        Log::info("Using fallback local data for organization {$organization->id}");

        // Get invoices from local database
        $invoices = Invoice::where('organization_id', $organization->id)->get();

        // Get recurring invoices (invoices marked as recurring)
        $recurringInvoices = $invoices->where('is_recurring', true);

        // Get draft invoices
        $drafts = $invoices->where('status', 'draft');

        return view('dashboard', [
            'invoices' => $invoices,
            'recurringInvoices' => $recurringInvoices,
            'drafts' => $drafts,
            'unpaidInvoices' => $invoices->whereNotIn('status', ['paid', 'cancelled'])->count(),
            'totalRevenue' => $invoices->where('status', 'paid')->sum('total'),
            'dataSource' => 'local_database'
        ]);
    }


}
