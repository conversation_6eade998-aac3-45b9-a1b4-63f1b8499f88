<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    protected $fillable = [
        'user_id',
        'organization_id',
        'bexio_id',
        'title',
        'document_nr',
        'contact_info',
        'total',
        'status',
        'is_recurring',
        'recurring_settings',
        'last_synced_at'
    ];

    protected $casts = [
        'contact_info' => 'array',
        'recurring_settings' => 'array',
        'is_recurring' => 'boolean',
        'total' => 'decimal:2',
        'last_synced_at' => 'datetime'
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function recurringTemplate()
    {
        return $this->hasOne(RecurringTemplate::class);
    }

    // Scopes
    public function scopeForOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Helper Methods
    public function isPaid()
    {
        return $this->status === 'paid';
    }

    public function isDraft()
    {
        return $this->status === 'draft';
    }

    public function isSent()
    {
        return $this->status === 'sent';
    }

    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }

    // Sync-related methods
    public function isFromBexio()
    {
        return !is_null($this->bexio_id);
    }

    public function needsSync()
    {
        if (!$this->isFromBexio()) {
            return false;
        }

        // Check if never synced or synced more than 1 hour ago
        return is_null($this->last_synced_at) ||
               $this->last_synced_at->lt(now()->subHour());
    }

    public function markAsSynced()
    {
        $this->update(['last_synced_at' => now()]);
    }

    // Scope for Bexio invoices
    public function scopeFromBexio($query)
    {
        return $query->whereNotNull('bexio_id');
    }

    public function scopeLocalOnly($query)
    {
        return $query->whereNull('bexio_id');
    }
}
